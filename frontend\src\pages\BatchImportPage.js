import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { PlayIcon, StopIcon, FolderIcon, ClockIcon, CheckCircleIcon, XCircleIcon } from '@heroicons/react/24/outline';

const BatchImportPage = () => {
    const [scanPath, setScanPath] = useState('/weiam');
    const [daysBefore, setDaysBefore] = useState(30);
    const [conflictStrategy, setConflictStrategy] = useState('overwrite');
    const [batchDelay, setBatchDelay] = useState(0.1);
    const [status, setStatus] = useState(null);
    const [isLoading, setIsLoading] = useState(false);
    const [mediaRoot, setMediaRoot] = useState('/weiam');

    // 获取媒体根路径
    useEffect(() => {
        axios.get('/api/settings')
            .then(res => {
                const root = res.data.media_root || '/weiam';
                setMediaRoot(root);
                setScanPath(root);
            })
            .catch(err => {
                console.error("获取设置失败:", err);
            });
    }, []);

    // 定期获取状态
    useEffect(() => {
        let interval;
        if (status && status.running) {
            interval = setInterval(() => {
                fetchStatus();
            }, 2000); // 每2秒更新一次
        }
        return () => {
            if (interval) clearInterval(interval);
        };
    }, [status]);

    const fetchStatus = async () => {
        try {
            const response = await axios.get('/api/batch-import/status');
            setStatus(response.data);
        } catch (error) {
            console.error('获取状态失败:', error);
        }
    };

    const startBatchImport = async () => {
        if (!scanPath.trim()) {
            alert('请输入扫描路径');
            return;
        }

        setIsLoading(true);
        try {
            const response = await axios.post('/api/batch-import/start', {
                scan_path: scanPath,
                days_before: daysBefore,
                conflict_strategy: conflictStrategy,
                batch_delay: batchDelay
            });

            if (response.data.success) {
                alert('批量导入已启动');
                fetchStatus();
            } else {
                alert(`启动失败: ${response.data.message}`);
            }
        } catch (error) {
            console.error('启动批量导入失败:', error);
            alert(`启动失败: ${error.response?.data?.message || error.message}`);
        } finally {
            setIsLoading(false);
        }
    };

    const stopBatchImport = async () => {
        try {
            const response = await axios.post('/api/batch-import/stop');
            if (response.data.success) {
                alert('停止指令已发送');
                fetchStatus();
            } else {
                alert(`停止失败: ${response.data.message}`);
            }
        } catch (error) {
            console.error('停止批量导入失败:', error);
            alert(`停止失败: ${error.response?.data?.message || error.message}`);
        }
    };

    const formatDuration = (startTime, endTime) => {
        if (!startTime) return '';
        
        const start = new Date(startTime);
        const end = endTime ? new Date(endTime) : new Date();
        const duration = Math.floor((end - start) / 1000);
        
        const hours = Math.floor(duration / 3600);
        const minutes = Math.floor((duration % 3600) / 60);
        const seconds = duration % 60;
        
        if (hours > 0) {
            return `${hours}小时${minutes}分${seconds}秒`;
        } else if (minutes > 0) {
            return `${minutes}分${seconds}秒`;
        } else {
            return `${seconds}秒`;
        }
    };

    return (
        <div className="min-h-screen bg-[var(--color-bg-primary)] text-[var(--color-primary-text)]">
            <div className="container mx-auto px-4 py-8">
                <div className="max-w-4xl mx-auto">
                    <h1 className="text-3xl font-bold text-[var(--color-primary-accent)] mb-8 flex items-center">
                        <FolderIcon className="h-8 w-8 mr-3" />
                        批量入库管理
                    </h1>

                    {/* 配置区域 */}
                    <div className="bg-[var(--color-bg-secondary)] rounded-lg p-6 mb-6 border border-[var(--color-border)]">
                        <h2 className="text-xl font-semibold mb-4 text-[var(--color-primary-accent)]">导入配置</h2>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label className="block text-sm font-medium mb-2">扫描路径</label>
                                <input
                                    type="text"
                                    value={scanPath}
                                    onChange={(e) => setScanPath(e.target.value)}
                                    className="w-full px-3 py-2 bg-[var(--color-bg-primary)] border border-[var(--color-border)] rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--color-primary-accent)]"
                                    placeholder="输入要扫描的目录路径"
                                    disabled={status && status.running}
                                />
                                <p className="text-sm text-[var(--color-secondary-text)] mt-1">
                                    将递归扫描此路径下的所有.strm文件
                                </p>
                            </div>

                            <div>
                                <label className="block text-sm font-medium mb-2">时间偏移（天）</label>
                                <input
                                    type="number"
                                    value={daysBefore}
                                    onChange={(e) => setDaysBefore(parseInt(e.target.value) || 30)}
                                    className="w-full px-3 py-2 bg-[var(--color-bg-primary)] border border-[var(--color-border)] rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--color-primary-accent)]"
                                    min="1"
                                    max="365"
                                    disabled={status && status.running}
                                />
                                <p className="text-sm text-[var(--color-secondary-text)] mt-1">
                                    设置入库时间比当前时间早多少天
                                </p>
                            </div>

                            <div>
                                <label className="block text-sm font-medium mb-2">冲突处理策略</label>
                                <select
                                    value={conflictStrategy}
                                    onChange={(e) => setConflictStrategy(e.target.value)}
                                    className="w-full px-3 py-2 bg-[var(--color-bg-primary)] border border-[var(--color-border)] rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--color-primary-accent)]"
                                    disabled={status && status.running}
                                >
                                    <option value="overwrite">覆盖已存在的记录</option>
                                    <option value="skip">跳过已存在的文件</option>
                                </select>
                                <p className="text-sm text-[var(--color-secondary-text)] mt-1">
                                    遇到已存在文件时的处理方式
                                </p>
                            </div>

                            <div>
                                <label className="block text-sm font-medium mb-2">处理速度（秒/文件）</label>
                                <input
                                    type="number"
                                    value={batchDelay}
                                    onChange={(e) => setBatchDelay(parseFloat(e.target.value) || 0.1)}
                                    className="w-full px-3 py-2 bg-[var(--color-bg-primary)] border border-[var(--color-border)] rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--color-primary-accent)]"
                                    min="0"
                                    max="10"
                                    step="0.1"
                                    disabled={status && status.running}
                                />
                                <p className="text-sm text-[var(--color-secondary-text)] mt-1">
                                    每个文件处理间隔，0表示最快速度
                                </p>
                            </div>
                        </div>

                        <div className="mt-6 flex gap-4">
                            <button
                                onClick={startBatchImport}
                                disabled={isLoading || (status && status.running)}
                                className="flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-500 text-white rounded-md transition-colors"
                            >
                                <PlayIcon className="h-5 w-5 mr-2" />
                                {isLoading ? '启动中...' : '开始批量导入'}
                            </button>

                            {status && status.running && (
                                <button
                                    onClick={stopBatchImport}
                                    className="flex items-center px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md transition-colors"
                                >
                                    <StopIcon className="h-5 w-5 mr-2" />
                                    停止导入
                                </button>
                            )}

                            <button
                                onClick={fetchStatus}
                                className="flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors"
                            >
                                刷新状态
                            </button>
                        </div>
                    </div>

                    {/* 状态显示区域 */}
                    {status && (
                        <div className="bg-[var(--color-bg-secondary)] rounded-lg p-6 border border-[var(--color-border)]">
                            <h2 className="text-xl font-semibold mb-4 text-[var(--color-primary-accent)]">导入状态</h2>
                            
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                                <div className="bg-[var(--color-bg-primary)] p-4 rounded-lg">
                                    <div className="text-sm text-[var(--color-secondary-text)]">状态</div>
                                    <div className={`text-lg font-semibold ${status.running ? 'text-green-500' : 'text-gray-500'}`}>
                                        {status.running ? '运行中' : '已停止'}
                                    </div>
                                </div>

                                <div className="bg-[var(--color-bg-primary)] p-4 rounded-lg">
                                    <div className="text-sm text-[var(--color-secondary-text)]">进度</div>
                                    <div className="text-lg font-semibold">
                                        {status.progress}/{status.total} ({status.progress_percent}%)
                                    </div>
                                </div>

                                <div className="bg-[var(--color-bg-primary)] p-4 rounded-lg">
                                    <div className="text-sm text-[var(--color-secondary-text)]">成功</div>
                                    <div className="text-lg font-semibold text-green-500 flex items-center">
                                        <CheckCircleIcon className="h-5 w-5 mr-1" />
                                        {status.success_count}
                                    </div>
                                </div>

                                <div className="bg-[var(--color-bg-primary)] p-4 rounded-lg">
                                    <div className="text-sm text-[var(--color-secondary-text)]">失败</div>
                                    <div className="text-lg font-semibold text-red-500 flex items-center">
                                        <XCircleIcon className="h-5 w-5 mr-1" />
                                        {status.error_count}
                                    </div>
                                </div>
                            </div>

                            {/* 进度条 */}
                            {status.total > 0 && (
                                <div className="mb-4">
                                    <div className="flex justify-between text-sm mb-1">
                                        <span>处理进度</span>
                                        <span>{status.progress_percent}%</span>
                                    </div>
                                    <div className="w-full bg-gray-200 rounded-full h-2">
                                        <div 
                                            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                                            style={{ width: `${status.progress_percent}%` }}
                                        ></div>
                                    </div>
                                </div>
                            )}

                            {/* 当前处理文件 */}
                            {status.current_file && (
                                <div className="mb-4">
                                    <div className="text-sm text-[var(--color-secondary-text)] mb-1">当前处理</div>
                                    <div className="text-sm font-mono bg-[var(--color-bg-primary)] p-2 rounded border">
                                        {status.current_file}
                                    </div>
                                </div>
                            )}

                            {/* 时间信息 */}
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                                {status.start_time && (
                                    <div>
                                        <div className="text-sm text-[var(--color-secondary-text)]">开始时间</div>
                                        <div className="text-sm">{status.start_time}</div>
                                    </div>
                                )}
                                
                                {status.end_time && (
                                    <div>
                                        <div className="text-sm text-[var(--color-secondary-text)]">结束时间</div>
                                        <div className="text-sm">{status.end_time}</div>
                                    </div>
                                )}
                                
                                <div>
                                    <div className="text-sm text-[var(--color-secondary-text)]">耗时</div>
                                    <div className="text-sm flex items-center">
                                        <ClockIcon className="h-4 w-4 mr-1" />
                                        {formatDuration(status.start_time, status.end_time)}
                                    </div>
                                </div>
                            </div>

                            {/* 错误信息 */}
                            {status.errors && status.errors.length > 0 && (
                                <div>
                                    <div className="text-sm text-[var(--color-secondary-text)] mb-2">错误详情</div>
                                    <div className="max-h-40 overflow-y-auto bg-[var(--color-bg-primary)] p-3 rounded border">
                                        {status.errors.slice(-10).map((error, index) => (
                                            <div key={index} className="text-sm mb-2 last:mb-0">
                                                <div className="text-red-500 font-mono">{error.file}</div>
                                                <div className="text-[var(--color-secondary-text)] ml-2">{error.error}</div>
                                            </div>
                                        ))}
                                        {status.errors.length > 10 && (
                                            <div className="text-sm text-[var(--color-secondary-text)] mt-2">
                                                ... 还有 {status.errors.length - 10} 个错误
                                            </div>
                                        )}
                                    </div>
                                </div>
                            )}
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default BatchImportPage;
