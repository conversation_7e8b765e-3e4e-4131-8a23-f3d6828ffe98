# backend/api/settings_api.py
"""
基础设置管理模块
处理系统设置的获取、保存、重启和通知测试功能
"""
import os
import signal
import time
import threading
import subprocess
import logging
from flask import Blueprint, request, jsonify, current_app
from config_utils import get_settings, save_settings, get_restart_required_settings, apply_performance_preset, get_performance_presets
from notification_sender import send_test_notification
import requests

# 创建设置API蓝图
settings_api = Blueprint('settings_api', __name__)

@settings_api.route('/settings', methods=['GET'])
def get_settings_route(): 
    """获取设置，并标记哪些设置需要重启"""
    try:
        settings = get_settings()
        restart_required = get_restart_required_settings()
        
        # 添加需要重启的设置标记
        return jsonify({
            "settings": settings,
            "restart_required_settings": restart_required
        })
    except Exception as e:
        current_app.logger.error(f"获取设置失败: {e}", exc_info=True)
        return jsonify({"success": False, "message": f"获取设置失败: {e}"}), 500

@settings_api.route('/settings', methods=['POST'])
def save_settings_route():
    """保存设置，并返回是否需要重启的信息"""
    try:
        new_settings = request.json
        if not new_settings:
            return jsonify({"success": False, "message": "无效的设置数据"}), 400
        
        # 获取当前设置，用于比较变化和合并
        current_settings = get_settings()

        # 合并设置：保持现有设置，只更新提供的新设置
        merged_settings = current_settings.copy()
        merged_settings.update(new_settings)

        # 保存合并后的设置
        success, message, restart_needed = save_settings(merged_settings, current_settings)
        
        if success:
            # 更新日志级别，这是唯一一个可以不重启就生效的"需要重启"的设置
            if 'log_level' in new_settings:
                log_level_str = new_settings.get('log_level', 'INFO').upper()
                old_log_level = current_settings.get('log_level', 'INFO').upper()

                # 只有在日志级别实际发生变化时才更新和提示
                if log_level_str != old_log_level:
                    new_level = getattr(logging, log_level_str, logging.INFO)
                    current_app.logger.setLevel(new_level)
                    for handler in current_app.logger.handlers:
                        handler.setLevel(new_level)
                    current_app.logger.info(f"日志级别已更新为: {log_level_str}")
            
            return jsonify({
                "success": True, 
                "message": message,
                "restart_needed": restart_needed
            })
        
        return jsonify({"success": False, "message": message}), 500
        
    except Exception as e:
        current_app.logger.error(f"保存设置失败: {e}", exc_info=True)
        return jsonify({"success": False, "message": f"保存设置失败: {e}"}), 500

@settings_api.route('/test-notification', methods=['POST'])
def test_notification_route():
    """测试通知发送功能并返回详细结果"""
    try:
        with current_app.app_context():
            # 获取当前设置以记录日志
            settings = get_settings()
            notification_api_url = settings.get('notification_api_url', '')
            notification_type = settings.get('notification_type', 'custom')

            # 记录测试开始日志
            current_app.logger.info(f"开始测试{notification_type}类型通知发送，"
                               f"API地址: {notification_api_url if notification_type == 'custom' else 'telegram'}")
            
            # --- 调用专用的测试函数 ---
            send_test_notification()
            return jsonify({"success": True, "message": "测试通知已发送，请检查您的通知服务。"})
            
    except requests.exceptions.Timeout as e:
        error_msg = f"发送测试通知超时: {e}"
        current_app.logger.error(error_msg, exc_info=True)
        return jsonify({"success": False, "message": error_msg}), 500
    except requests.exceptions.ConnectionError as e:
        error_msg = f"发送测试通知连接失败: {e}"
        current_app.logger.error(error_msg, exc_info=True)
        return jsonify({"success": False, "message": error_msg}), 500
    except ValueError as e:
        error_msg = f"通知配置错误: {e}"
        current_app.logger.error(error_msg, exc_info=True)
        return jsonify({"success": False, "message": error_msg}), 400
    except Exception as e:
        error_msg = f"发送测试通知失败: {e}"
        current_app.logger.error(error_msg, exc_info=True)
        return jsonify({"success": False, "message": error_msg}), 500

@settings_api.route('/restart-container', methods=['POST'])
def restart_container():
    """重启容器内的服务"""
    try:
        current_app.logger.info("收到重启容器请求")
        
        # 方法1: 发送信号给supervisor主进程重启所有服务
        try:
            current_app.logger.info("尝试向supervisor发送重启信号")
            
            # 查找supervisor主进程PID
            with open("/var/run/supervisord.pid", 'r') as f:
                supervisor_pid = int(f.read().strip())
            
            # 发送SIGHUP信号重新加载配置并重启服务
            os.kill(supervisor_pid, signal.SIGHUP)
            
            current_app.logger.info(f"=== 服务重启成功 ===")
            current_app.logger.info(f"已向supervisor进程({supervisor_pid})发送SIGHUP信号")
            return jsonify({
                "success": True,
                "message": "服务正在重启，请稍后刷新页面"
            })
            
        except Exception as e:
            current_app.logger.warning(f"向supervisor发送信号失败: {e}")
        
        # 方法2: 创建重启脚本异步执行
        try:
            current_app.logger.info("尝试使用重启脚本")
            
            # 创建重启脚本，直接杀死当前进程让supervisor重启
            restart_script = """#!/bin/bash
sleep 2
# 杀死gunicorn主进程，supervisor会自动重启
pkill -f "gunicorn.*app:app" || true
# 杀死scheduler进程，supervisor会自动重启  
pkill -f "scheduler_standalone.py" || true
"""
            script_path = "/tmp/restart_services.sh"
            with open(script_path, 'w') as f:
                f.write(restart_script)
            os.chmod(script_path, 0o755)
            
            # 异步执行重启脚本
            subprocess.Popen(["/bin/bash", script_path], 
                           stdout=subprocess.DEVNULL, 
                           stderr=subprocess.DEVNULL)
            
            return jsonify({
                "success": True,
                "message": "服务正在重启，请稍后刷新页面"
            })
            
        except Exception as e:
            current_app.logger.warning(f"重启脚本执行失败: {e}")
        
        # 方法3: 直接退出当前进程，让supervisor自动重启
        try:
            current_app.logger.info("使用进程退出方式触发重启")
            
            def delayed_exit():
                time.sleep(2)
                current_app.logger.info("执行进程退出")
                os._exit(1)  # 强制退出，supervisor会自动重启
            
            # 异步执行退出
            threading.Thread(target=delayed_exit, daemon=True).start()
            
            return jsonify({
                "success": True,
                "message": "服务正在重启，请稍后刷新页面"
            })
            
        except Exception as e:
            current_app.logger.error(f"进程退出方式失败: {e}")
        
        # 如果所有方法都失败
        return jsonify({
            "success": False,
            "message": "无法重启服务，请手动重启容器"
        }), 500
        
    except Exception as e:
        current_app.logger.error(f"重启容器失败: {e}", exc_info=True)
        return jsonify({"success": False, "message": f"重启失败: {e}"}), 500

@settings_api.route('/update-log-level', methods=['POST'])
def update_log_level():
    """更新日志级别"""
    try:
        data = request.json
        if not data:
            return jsonify({"success": False, "message": "无效的请求数据"}), 400

        log_level = data.get('log_level', 'INFO').upper()

        # 验证日志级别是否有效
        if log_level not in ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']:
            return jsonify({"success": False, "message": "无效的日志级别"}), 400

        # 获取当前设置
        settings = get_settings()
        old_log_level = settings.get('log_level', 'INFO').upper()

        # 只有在日志级别实际发生变化时才更新
        if log_level.upper() != old_log_level:
            # 更新日志级别
            settings['log_level'] = log_level
            success, message, restart_needed = save_settings(settings)

            if success:
                # 更新当前应用的日志级别
                new_level = getattr(logging, log_level, logging.INFO)
                current_app.logger.setLevel(new_level)
                for handler in current_app.logger.handlers:
                    handler.setLevel(new_level)

                current_app.logger.info(f"日志级别已更新为: {log_level}")
                return jsonify({"success": True, "message": f"日志级别已更新为: {log_level}"})
            else:
                return jsonify({"success": False, "message": message}), 500
        else:
            # 日志级别没有变化，直接返回成功
            return jsonify({"success": True, "message": f"日志级别保持为: {log_level}"})

    except Exception as e:
        current_app.logger.error(f"更新日志级别失败: {str(e)}", exc_info=True)
        return jsonify({"success": False, "message": f"更新日志级别失败: {str(e)}"}), 500

@settings_api.route('/performance-presets', methods=['GET'])
def get_performance_presets_route():
    """获取性能预设配置"""
    try:
        presets = get_performance_presets()
        return jsonify({"success": True, "presets": presets})
    except Exception as e:
        current_app.logger.error(f"获取性能预设失败: {e}", exc_info=True)
        return jsonify({"success": False, "message": f"获取性能预设失败: {e}"}), 500

@settings_api.route('/apply-performance-preset', methods=['POST'])
def apply_performance_preset_route():
    """应用性能预设配置"""
    try:
        data = request.json
        if not data or 'mode' not in data:
            return jsonify({"success": False, "message": "缺少性能模式参数"}), 400

        mode = data['mode']
        if mode not in ['low', 'medium', 'high']:
            return jsonify({"success": False, "message": "无效的性能模式"}), 400

        # 获取当前设置
        current_settings = get_settings()

        # 应用性能预设
        updated_settings = apply_performance_preset(current_settings, mode)

        # 保存设置
        success, message, restart_needed = save_settings(updated_settings, current_settings)

        if success:
            return jsonify({
                "success": True,
                "message": f"已应用{mode}性能模式配置",
                "restart_needed": restart_needed,
                "settings": updated_settings
            })

        return jsonify({"success": False, "message": message}), 500

    except Exception as e:
        current_app.logger.error(f"应用性能预设失败: {e}", exc_info=True)
        return jsonify({"success": False, "message": f"应用性能预设失败: {e}"}), 500

@settings_api.route('/config-file-status', methods=['GET'])
def get_config_file_status():
    """获取配置文件状态信息"""
    try:
        from config_utils import get_settings_file_path
        import os
        import stat

        config_file = get_settings_file_path()
        config_dir = os.path.dirname(config_file)

        status = {
            'config_file_path': config_file,
            'config_dir_path': config_dir,
            'config_file_exists': os.path.exists(config_file),
            'config_dir_exists': os.path.exists(config_dir),
            'config_dir_writable': os.access(config_dir, os.W_OK) if os.path.exists(config_dir) else False,
            'config_file_writable': os.access(config_file, os.W_OK) if os.path.exists(config_file) else False
        }

        # 获取文件信息
        if os.path.exists(config_file):
            file_stat = os.stat(config_file)
            status['file_size'] = file_stat.st_size
            status['file_mtime'] = file_stat.st_mtime
            status['file_permissions'] = oct(stat.S_IMODE(file_stat.st_mode))

        # 获取目录信息
        if os.path.exists(config_dir):
            dir_stat = os.stat(config_dir)
            status['dir_permissions'] = oct(stat.S_IMODE(dir_stat.st_mode))

        return jsonify({
            "success": True,
            "data": status
        })

    except Exception as e:
        current_app.logger.error(f"获取配置文件状态失败: {e}", exc_info=True)
        return jsonify({
            "success": False,
            "message": f"获取配置文件状态失败: {str(e)}"
        }), 500


