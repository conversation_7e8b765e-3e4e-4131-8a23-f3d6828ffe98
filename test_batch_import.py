#!/usr/bin/env python3
"""
批量导入功能测试脚本
"""
import requests
import time
import json

BASE_URL = "http://localhost:34712/api"

def test_batch_import():
    """测试批量导入功能"""
    
    print("=== 批量导入功能测试 ===")
    
    # 1. 获取初始状态
    print("\n1. 获取初始状态...")
    try:
        response = requests.get(f"{BASE_URL}/batch-import/status")
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            status = response.json()
            print(f"初始状态: {json.dumps(status, indent=2, ensure_ascii=False)}")
        else:
            print(f"获取状态失败: {response.text}")
    except Exception as e:
        print(f"请求失败: {e}")
        return
    
    # 2. 启动批量导入
    print("\n2. 启动批量导入...")
    try:
        data = {
            "scan_path": "/weiam",
            "days_before": 30
        }
        response = requests.post(f"{BASE_URL}/batch-import/start", json=data)
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"启动结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
        else:
            print(f"启动失败: {response.text}")
            return
    except Exception as e:
        print(f"请求失败: {e}")
        return
    
    # 3. 监控进度
    print("\n3. 监控进度...")
    for i in range(10):  # 最多监控10次
        try:
            time.sleep(2)  # 等待2秒
            response = requests.get(f"{BASE_URL}/batch-import/status")
            if response.status_code == 200:
                status = response.json()
                print(f"进度: {status.get('progress', 0)}/{status.get('total', 0)} "
                      f"({status.get('progress_percent', 0)}%) "
                      f"成功: {status.get('success_count', 0)} "
                      f"失败: {status.get('error_count', 0)} "
                      f"状态: {'运行中' if status.get('running') else '已停止'}")
                
                if not status.get('running'):
                    print("批量导入已完成")
                    break
            else:
                print(f"获取状态失败: {response.text}")
        except Exception as e:
            print(f"监控失败: {e}")
    
    # 4. 获取最终状态
    print("\n4. 获取最终状态...")
    try:
        response = requests.get(f"{BASE_URL}/batch-import/status")
        if response.status_code == 200:
            status = response.json()
            print(f"最终状态: {json.dumps(status, indent=2, ensure_ascii=False)}")
        else:
            print(f"获取状态失败: {response.text}")
    except Exception as e:
        print(f"请求失败: {e}")

if __name__ == "__main__":
    test_batch_import()
