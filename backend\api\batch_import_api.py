# backend/api/batch_import_api.py
"""
批量入库API模块
负责批量扫描和导入存量NFO和图片文件
"""
import os
import time
import threading
from datetime import datetime, timedelta
from flask import Blueprint, request, jsonify, current_app
from db_manager import get_db_connection
from image_processor import get_image_details
from nfo_parser import parse_nfo_file
from webhook_handler import extract_bangou_from_title, extract_strm_name_from_path, handle_nfo_mappings
from api.media_api import get_media_root, is_safe_path

# 创建批量入库API蓝图
batch_import_api = Blueprint('batch_import_api', __name__)

# 批量导入状态管理
batch_import_status = {
    'running': False,
    'progress': 0,
    'total': 0,
    'current_file': '',
    'start_time': None,
    'end_time': None,
    'success_count': 0,
    'error_count': 0,
    'errors': [],
    'thread': None
}

def find_strm_files(root_path, max_depth=10):
    """
    递归查找所有.strm文件
    
    Args:
        root_path: 根目录路径
        max_depth: 最大递归深度，防止无限递归
        
    Returns:
        list: strm文件路径列表
    """
    strm_files = []
    
    def _scan_directory(path, current_depth=0):
        if current_depth > max_depth:
            return
            
        try:
            for item in os.listdir(path):
                item_path = os.path.join(path, item)
                
                if os.path.isfile(item_path) and item.lower().endswith('.strm'):
                    strm_files.append(item_path)
                elif os.path.isdir(item_path):
                    _scan_directory(item_path, current_depth + 1)
        except (PermissionError, FileNotFoundError, OSError) as e:
            current_app.logger.warning(f"扫描目录失败: {path} - {e}")
    
    _scan_directory(root_path)
    return strm_files

def process_single_strm_file(strm_path, base_timestamp, conflict_strategy='overwrite'):
    """
    处理单个strm文件，导入相关的NFO和图片数据

    Args:
        strm_path: strm文件路径
        base_timestamp: 基础时间戳，用于设置created_at时间
        conflict_strategy: 冲突处理策略，'overwrite'(覆盖) 或 'skip'(跳过)

    Returns:
        dict: 处理结果
    """
    try:
        # 提取文件信息
        item_name = os.path.basename(strm_path)
        bangou, clean_title = extract_bangou_from_title(item_name)
        strm_name = extract_strm_name_from_path(strm_path)
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        try:
            # 检查是否已存在
            if conflict_strategy == 'skip':
                cursor.execute("SELECT id FROM movies WHERE item_path = ?", (strm_path,))
                existing = cursor.fetchone()
                if existing:
                    return {"success": True, "message": f"跳过已存在的文件 {item_name}", "skipped": True}

            # 插入或更新movies表，使用指定的时间戳
            if conflict_strategy == 'overwrite':
                cursor.execute("""
                    INSERT INTO movies (item_path, bangou, title, created_at)
                    VALUES (?, ?, ?, ?)
                    ON CONFLICT(item_path) DO UPDATE SET
                        title=excluded.title,
                        bangou=excluded.bangou,
                        created_at=CASE
                            WHEN created_at > excluded.created_at THEN created_at
                            ELSE excluded.created_at
                        END
                """, (strm_path, bangou, clean_title, base_timestamp))
            else:
                cursor.execute("""
                    INSERT INTO movies (item_path, bangou, title, created_at)
                    VALUES (?, ?, ?, ?)
                """, (strm_path, bangou, clean_title, base_timestamp))
            
            cursor.execute("SELECT id FROM movies WHERE item_path = ?", (strm_path,))
            movie_id = cursor.fetchone()['id']
            base_path = os.path.splitext(strm_path)[0]
            
            # 处理图片文件
            poster_path = f"{base_path}-poster.jpg"
            fanart_path = f"{base_path}-fanart.jpg"
            thumb_path = f"{base_path}-thumb.jpg"
            
            # 获取图片详细信息
            p_w, p_h, p_s_kb, p_stat = get_image_details(poster_path)
            f_w, f_h, f_s_kb, f_stat = get_image_details(fanart_path)
            t_w, t_h, t_s_kb, t_stat = get_image_details(thumb_path)
            
            # 插入或更新图片信息
            cursor.execute("""
                INSERT INTO pictures (
                    movie_id, poster_path, poster_width, poster_height, poster_size_kb, poster_status,
                    fanart_path, fanart_width, fanart_height, fanart_size_kb, fanart_status,
                    thumb_path, thumb_width, thumb_height, thumb_size_kb, thumb_status
                ) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) 
                ON CONFLICT(movie_id) DO UPDATE SET 
                    poster_path=excluded.poster_path, poster_width=excluded.poster_width, 
                    poster_height=excluded.poster_height, poster_size_kb=excluded.poster_size_kb, 
                    poster_status=excluded.poster_status, fanart_path=excluded.fanart_path, 
                    fanart_width=excluded.fanart_width, fanart_height=excluded.fanart_height, 
                    fanart_size_kb=excluded.fanart_size_kb, fanart_status=excluded.fanart_status, 
                    thumb_path=excluded.thumb_path, thumb_width=excluded.thumb_width, 
                    thumb_height=excluded.thumb_height, thumb_size_kb=excluded.thumb_size_kb, 
                    thumb_status=excluded.thumb_status
            """, (movie_id, f"{base_path}-poster.jpg", p_w, p_h, p_s_kb, p_stat, 
                  f"{base_path}-fanart.jpg", f_w, f_h, f_s_kb, f_stat, 
                  f"{base_path}-thumb.jpg", t_w, t_h, t_s_kb, t_stat))
            
            # 处理NFO文件
            dir_path = os.path.dirname(strm_path)
            processed_nfo_paths = set()
            
            # 首先尝试查找与strm文件同名的nfo文件
            nfo_file_name = f"{strm_name}.nfo"
            nfo_file_path = os.path.join(dir_path, nfo_file_name)
            
            if os.path.exists(nfo_file_path):
                processed_nfo_paths.add(nfo_file_path)
                nfo_data = parse_nfo_file(nfo_file_path)
                if nfo_data:
                    nfo_main_cols = ['originaltitle', 'plot', 'originalplot', 'tagline', 'release_date', 'year', 'rating', 'criticrating']
                    nfo_main_vals = [nfo_data.get(col) for col in nfo_main_cols]
                    
                    cursor.execute(f"""
                        INSERT INTO nfo_data (movie_id, nfo_path, strm_name, {', '.join(nfo_main_cols)}) 
                        VALUES (?, ?, ?, {', '.join(['?'] * len(nfo_main_cols))}) 
                        ON CONFLICT(strm_name, nfo_path) DO UPDATE SET {', '.join([f"{col}=excluded.{col}" for col in nfo_main_cols])}
                    """, (movie_id, nfo_file_path, strm_name, *nfo_main_vals))
                    
                    cursor.execute("SELECT id FROM nfo_data WHERE movie_id = ? AND nfo_path = ?", (movie_id, nfo_file_path))
                    nfo_id_row = cursor.fetchone()
                    if nfo_id_row:
                        nfo_id = nfo_id_row['id']
                        handle_nfo_mappings(cursor, nfo_id, nfo_data)
            else:
                # 查找包含相同番号的NFO文件
                try:
                    for filename in os.listdir(dir_path):
                        if bangou.lower() in filename.lower() and filename.lower().endswith('.nfo'):
                            nfo_file_path = os.path.join(dir_path, filename)
                            
                            if nfo_file_path in processed_nfo_paths:
                                continue
                            
                            processed_nfo_paths.add(nfo_file_path)
                            nfo_data = parse_nfo_file(nfo_file_path)
                            if nfo_data:
                                nfo_main_cols = ['originaltitle', 'plot', 'originalplot', 'tagline', 'release_date', 'year', 'rating', 'criticrating']
                                nfo_main_vals = [nfo_data.get(col) for col in nfo_main_cols]
                                
                                cursor.execute(f"""
                                    INSERT INTO nfo_data (movie_id, nfo_path, strm_name, {', '.join(nfo_main_cols)}) 
                                    VALUES (?, ?, ?, {', '.join(['?'] * len(nfo_main_cols))}) 
                                    ON CONFLICT(strm_name, nfo_path) DO UPDATE SET {', '.join([f"{col}=excluded.{col}" for col in nfo_main_cols])}
                                """, (movie_id, nfo_file_path, strm_name, *nfo_main_vals))
                                
                                cursor.execute("SELECT id FROM nfo_data WHERE movie_id = ? AND nfo_path = ?", (movie_id, nfo_file_path))
                                nfo_id_row = cursor.fetchone()
                                if nfo_id_row:
                                    nfo_id = nfo_id_row['id']
                                    handle_nfo_mappings(cursor, nfo_id, nfo_data)
                except (PermissionError, FileNotFoundError, OSError):
                    pass  # 忽略目录访问错误
            
            conn.commit()
            return {"success": True, "message": f"成功处理 {item_name}"}
            
        except Exception as e:
            conn.rollback()
            raise e
        finally:
            conn.close()
            
    except Exception as e:
        current_app.logger.error(f"处理strm文件失败: {strm_path} - {e}", exc_info=True)
        return {"success": False, "message": f"处理失败: {e}"}

def batch_import_worker(scan_path, days_before=30, conflict_strategy='overwrite', batch_delay=0.1):
    """
    批量导入工作线程

    Args:
        scan_path: 扫描路径
        days_before: 设置created_at时间比当前时间早多少天
        conflict_strategy: 冲突处理策略，'overwrite'(覆盖) 或 'skip'(跳过)
        batch_delay: 每个文件处理间隔（秒），控制处理速度
    """
    global batch_import_status

    try:
        batch_import_status['running'] = True
        batch_import_status['start_time'] = datetime.now()
        batch_import_status['progress'] = 0
        batch_import_status['success_count'] = 0
        batch_import_status['error_count'] = 0
        batch_import_status['errors'] = []
        batch_import_status['current_file'] = '正在扫描文件...'

        current_app.logger.info(f"开始批量导入，扫描路径: {scan_path}")

        # 计算基础时间戳（比当前时间早指定天数）
        base_timestamp = datetime.now() - timedelta(days=days_before)
        base_timestamp_str = base_timestamp.strftime('%Y-%m-%d %H:%M:%S')

        # 扫描所有strm文件
        strm_files = find_strm_files(scan_path)
        batch_import_status['total'] = len(strm_files)

        current_app.logger.info(f"找到 {len(strm_files)} 个strm文件")

        if len(strm_files) == 0:
            batch_import_status['current_file'] = '未找到任何strm文件'
            return

        # 处理每个strm文件
        for i, strm_path in enumerate(strm_files):
            if not batch_import_status['running']:  # 检查是否被取消
                break

            batch_import_status['current_file'] = os.path.basename(strm_path)
            batch_import_status['progress'] = i + 1

            # 为每个文件设置不同的时间戳，避免完全相同
            file_timestamp = base_timestamp + timedelta(seconds=i)
            file_timestamp_str = file_timestamp.strftime('%Y-%m-%d %H:%M:%S')

            result = process_single_strm_file(strm_path, file_timestamp_str, conflict_strategy)

            if result['success']:
                batch_import_status['success_count'] += 1
            else:
                batch_import_status['error_count'] += 1
                batch_import_status['errors'].append({
                    'file': strm_path,
                    'error': result['message']
                })

            # 控制处理速度
            if batch_delay > 0:
                time.sleep(batch_delay)

            # 每处理100个文件记录一次进度
            if (i + 1) % 100 == 0:
                current_app.logger.info(f"批量导入进度: {i + 1}/{len(strm_files)}")

        batch_import_status['current_file'] = '导入完成'
        current_app.logger.info(f"批量导入完成，成功: {batch_import_status['success_count']}, 失败: {batch_import_status['error_count']}")

    except Exception as e:
        current_app.logger.error(f"批量导入过程中发生错误: {e}", exc_info=True)
        batch_import_status['errors'].append({
            'file': 'SYSTEM',
            'error': f"系统错误: {e}"
        })
    finally:
        batch_import_status['running'] = False
        batch_import_status['end_time'] = datetime.now()

@batch_import_api.route('/batch-import/start', methods=['POST'])
def start_batch_import():
    """启动批量导入"""
    global batch_import_status

    if batch_import_status['running']:
        return jsonify({"success": False, "message": "批量导入正在进行中"}), 400

    data = request.json or {}
    scan_path = data.get('scan_path', get_media_root())
    days_before = data.get('days_before', 30)
    conflict_strategy = data.get('conflict_strategy', 'overwrite')  # 'overwrite' 或 'skip'
    batch_delay = data.get('batch_delay', 0.1)  # 处理间隔，秒

    # 安全检查
    if not is_safe_path(scan_path):
        return jsonify({"success": False, "message": "无效的扫描路径"}), 400

    if not os.path.exists(scan_path):
        return jsonify({"success": False, "message": "扫描路径不存在"}), 400

    if not os.path.isdir(scan_path):
        return jsonify({"success": False, "message": "扫描路径不是目录"}), 400

    # 启动后台线程
    thread = threading.Thread(
        target=batch_import_worker,
        args=(scan_path, days_before, conflict_strategy, batch_delay),
        daemon=True
    )
    thread.start()
    batch_import_status['thread'] = thread

    return jsonify({
        "success": True,
        "message": "批量导入已启动",
        "scan_path": scan_path,
        "days_before": days_before
    })

@batch_import_api.route('/batch-import/status', methods=['GET'])
def get_batch_import_status():
    """获取批量导入状态"""
    status = batch_import_status.copy()

    # 转换时间格式
    if status['start_time']:
        status['start_time'] = status['start_time'].strftime('%Y-%m-%d %H:%M:%S')
    if status['end_time']:
        status['end_time'] = status['end_time'].strftime('%Y-%m-%d %H:%M:%S')

    # 计算进度百分比
    if status['total'] > 0:
        status['progress_percent'] = round((status['progress'] / status['total']) * 100, 2)
    else:
        status['progress_percent'] = 0

    # 移除线程对象（不能序列化）
    status.pop('thread', None)

    return jsonify(status)

@batch_import_api.route('/batch-import/stop', methods=['POST'])
def stop_batch_import():
    """停止批量导入"""
    global batch_import_status

    if not batch_import_status['running']:
        return jsonify({"success": False, "message": "没有正在进行的批量导入"}), 400

    batch_import_status['running'] = False
    batch_import_status['current_file'] = '正在停止...'

    return jsonify({"success": True, "message": "批量导入停止指令已发送"})
