# Jassistant - 媒体文件助手

![Jassistant Logo](https://raw.githubusercontent.com/fishinsevens/Jassistant/main/logo.png)

## 项目简介

Jassistant是一款专为JAV媒体的工具，通过webhook获取EMBY最新媒体首页显示，可以查找高清替换，支持水印处理，NFO编辑等功能。

数据清洗只支持 通过 EMBY webhook 获取后写入数据库的影片

未写入数据库的影片在文件管理中双击 NFO 使用手作修正

## 版本历史

- **1.0.0** - 初始版本也是~最终版本~
- **1.0.1** - 新增TG机器人通知
- **1.0.2** - 解除后台限制媒体库根目录，适应不同 EMBY 映射关系
- **1.0.3** - 修复日志显示，增加首页图像缓存等
- **1.0.4** - DMM链接验证缓存，避免重复验证已确认存在的图片，一些修复
- **1.0.5** - 远程图片预加载缓存，提升图片处理速度，一些修改

## 功能特点

- **最新入库展示**：通过webhook获取EMBY最新添加的媒体
- **高清替换**：查找并替换低画质媒体封面
- **数据清洗**：修正数据库中的媒体信息
- **手作修正**：修改未在数据库中的NFO文件

## 技术栈

- **后端**：Python + Flask
- **前端**：React + Tailwind CSS
- **数据库**：SQLite
- **容器化**：Docker

## 快速开始

### 使用Docker Compose

#### 标准版本
```yaml
services:
  jassistant:
    image: aliez0lie1/jassistant:latest
    container_name: jassistant
    ports:
      - "34711:34711"
    volumes:
      - ./data/logs:/app/logs
      - ./data/db:/app/db
      - ./data/settings:/app/settings
      - ./data/watermarks:/app/assets
      - ./data/cache:/app/data/cache  # 整个缓存目录
      #- ./data/images:/app/data/cache/images  # 或者只映射图片缓存
      - ./data/cover_cache:/app/cover_cache  # 封面缓存目录
      - /your/media/path:/weiam
    environment:
      - TZ=Asia/Shanghai
      - CID_API_KEY=your_api_key
      - CID_API_URL=your_api_url
    restart: unless-stopped
```

#### Alpine优化版本
```yaml
services:
  jassistant:
    image: aliez0lie1/jassistant:alpine
    container_name: jassistant
    ports:
      - "34711:34711"
    volumes:
      - ./data/logs:/app/logs
      - ./data/db:/app/db
      - ./data/settings:/app/settings
      - ./data/watermarks:/app/assets
      - ./data/cache:/app/data/cache  # 映射缓存目录
      #- ./data/images:/app/data/cache/images  # 或者映射图片缓存目录
      - ./data/cover_cache:/app/cover_cache  # 封面缓存目录
      - /your/media/path:/weiam
    environment:
      - TZ=Asia/Shanghai
      - CID_API_KEY=your_api_key
      - CID_API_URL=your_api_url
    restart: unless-stopped
```

## 配置说明

### 环境变量

| 环境变量 | 描述 | 默认值 |
| --- | --- | --- |
| TZ | 时区 | Asia/Shanghai |
| CID_API_KEY | CID API密钥 | - |
| CID_API_URL | CID API URL | - |

CID_API_KEY，CID_API_URL为 aliez0lie1/avlink 番号与 DMM CID 匹配的API接口 不配置则不可使用 DMM 查询，手动输入番号/CID 查询

### 卷挂载

| 挂载点 | 描述 |
| --- | --- |
| /app/logs | 日志文件 |
| /app/db | 数据库文件 |
| /app/settings | 设置文件 |
| /app/assets | 水印资源文件 |
| /app/data/cache | 缓存目录 |
| /app/cover_cache| 首页图像缓存 |
| /weiam | 媒体文件目录，可以修改为其他路径名，但需要在设置页面中相应更新"媒体根路径"设置，与EMBY媒体库保持一致映射 |

### 水印资源文件请自备喜欢的

命名如下：

4K：4k.png

8K：8k.png

字幕：subs.png

破解：cracked.png

流出：leaked.png

有码：mosaic.png

无码：uncensored.png

### EMBY 通知设置

http://localhost:34711/api/webhook

application/json

勾选媒体库-新媒体已添加

## 批量入库功能

### 功能说明

批量入库功能可以扫描指定目录下的所有.strm文件，并将相关的NFO和图片文件批量导入到数据库中。

### 主要特性

- **递归扫描**：自动扫描指定目录及其所有子目录中的.strm文件
- **时间控制**：可设置入库时间比当前时间早指定天数，避免在"最新入库"中显示
- **进度监控**：实时显示处理进度、成功/失败统计
- **错误处理**：详细记录处理过程中的错误信息
- **后台处理**：使用独立线程处理，不阻塞界面操作

### 使用方法

1. 访问"批量入库"页面
2. 设置扫描路径（默认为媒体根路径）
3. 设置时间偏移天数（默认30天）
4. 点击"开始批量导入"
5. 监控处理进度和结果

### API接口

- `POST /api/batch-import/start` - 启动批量导入
- `GET /api/batch-import/status` - 获取导入状态
- `POST /api/batch-import/stop` - 停止批量导入

## 浏览器访问

启动容器后，打开浏览器访问：`http://localhost:34711`