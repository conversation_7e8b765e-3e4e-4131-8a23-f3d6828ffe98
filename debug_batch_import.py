#!/usr/bin/env python3
"""
批量导入调试脚本
"""
import os
import sys

def test_path_and_files(scan_path="/weiam"):
    """测试路径和文件扫描"""
    print(f"=== 调试批量导入路径和文件扫描 ===")
    print(f"扫描路径: {scan_path}")
    
    # 1. 检查路径是否存在
    print(f"\n1. 路径检查:")
    if os.path.exists(scan_path):
        print(f"✓ 路径存在: {scan_path}")
        print(f"✓ 是目录: {os.path.isdir(scan_path)}")
    else:
        print(f"✗ 路径不存在: {scan_path}")
        return
    
    # 2. 检查权限
    print(f"\n2. 权限检查:")
    try:
        items = os.listdir(scan_path)
        print(f"✓ 可读取目录，包含 {len(items)} 个项目")
        if len(items) > 0:
            print(f"前5个项目: {items[:5]}")
    except PermissionError as e:
        print(f"✗ 权限错误: {e}")
        return
    except Exception as e:
        print(f"✗ 其他错误: {e}")
        return
    
    # 3. 查找strm文件
    print(f"\n3. 查找strm文件:")
    strm_files = []
    total_dirs = 0
    total_files = 0
    
    for root, dirs, files in os.walk(scan_path):
        total_dirs += len(dirs)
        total_files += len(files)
        
        for file in files:
            if file.lower().endswith('.strm'):
                full_path = os.path.join(root, file)
                strm_files.append(full_path)
                print(f"找到strm文件: {full_path}")
        
        # 只显示前几个目录的详情
        if len(strm_files) < 10:
            if files:
                print(f"目录 {root}: {len(files)} 个文件")
    
    print(f"\n=== 扫描结果 ===")
    print(f"总目录数: {total_dirs}")
    print(f"总文件数: {total_files}")
    print(f"strm文件数: {len(strm_files)}")
    
    if len(strm_files) == 0:
        print("\n⚠️  未找到任何.strm文件")
        print("请检查:")
        print("1. 路径是否正确")
        print("2. 是否有.strm文件存在")
        print("3. 文件扩展名是否正确")
    else:
        print(f"\n✓ 找到 {len(strm_files)} 个strm文件")

if __name__ == "__main__":
    # 可以通过命令行参数指定路径
    scan_path = sys.argv[1] if len(sys.argv) > 1 else "/weiam"
    test_path_and_files(scan_path)
