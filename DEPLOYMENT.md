# Jassistant 部署指南

## 批量入库功能部署

### 1. 构建Docker镜像

```bash
# 给构建脚本执行权限
chmod +x build.sh

# 构建镜像
./build.sh
```

### 2. 启动容器

```bash
# 给启动脚本执行权限
chmod +x start.sh

# 启动容器
./start.sh
```

或者直接使用docker-compose：

```bash
docker-compose up -d
```

### 3. 访问应用

- 应用地址：http://localhost:34712
- 批量入库页面：http://localhost:34712/batch-import

### 4. 配置说明

#### 环境变量

- `TZ`: 时区设置，默认为 Asia/Shanghai
- `CID_API_KEY`: CID API密钥
- `CID_API_URL`: CID API URL

#### 卷挂载

- `/app/logs`: 日志文件目录
- `/app/db`: 数据库文件目录
- `/app/settings`: 配置文件目录
- `/app/assets`: 水印资源文件目录
- `/app/data/cache`: 缓存目录
- `/app/cover_cache`: 封面缓存目录
- `/weiam`: 媒体文件目录（可修改）

### 5. 批量入库使用

1. 访问批量入库页面
2. 设置扫描路径（默认为媒体根路径 /weiam）
3. 设置时间偏移天数（默认30天，表示入库时间比当前时间早30天）
4. 点击"开始批量导入"
5. 监控处理进度

### 6. API接口

#### 启动批量导入
```
POST /api/batch-import/start
Content-Type: application/json

{
  "scan_path": "/weiam",
  "days_before": 30
}
```

#### 获取导入状态
```
GET /api/batch-import/status
```

#### 停止批量导入
```
POST /api/batch-import/stop
```

### 7. 测试

使用提供的测试脚本：

```bash
python3 test_batch_import.py
```

### 8. 故障排除

#### 查看日志
```bash
# 查看容器日志
docker-compose logs -f

# 查看应用日志
docker exec -it jassistant-test tail -f /app/logs/app.log
```

#### 常见问题

1. **权限问题**：确保媒体目录有正确的读取权限
2. **路径问题**：确保扫描路径存在且可访问
3. **内存不足**：大量文件处理时可能需要更多内存

### 9. 性能优化

- 对于大量文件，建议分批处理
- 监控系统资源使用情况
- 适当调整时间偏移避免影响正常使用

### 10. 安全注意事项

- 确保只有授权用户可以访问批量导入功能
- 定期备份数据库
- 监控系统日志
